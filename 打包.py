# -*- coding: utf-8 -*-
"""
AI命名工具打包脚本
使用PyInstaller将Python程序打包成exe文件
"""
import os
import subprocess

# ==================== 配置参数 ====================
PYTHON_FILE = "AI命名工具 命令行版.py"    # 要打包的Python文件
ICON_FILE = "Balloon.ico"                # 图标文件
EXE_NAME = "AI命名工具 命令行版"         # 生成的exe文件名
KEEP_CONSOLE = True                      # 是否保留控制台窗口
CLEAN_DIST = False                       # 是否清理dist文件夹（会删除已有exe）
# ================================================

def clean_cache():
    """清理打包缓存"""
    print("清理缓存...")

    import shutil
    from pathlib import Path

    # 根据配置决定是否清理dist文件夹
    cleanup_items = ["build", "__pycache__"]
    if CLEAN_DIST:
        cleanup_items.append("dist")
        print("⚠️  将清理dist文件夹（会删除已有的exe文件）")
    else:
        print("ℹ️  保留dist文件夹中的已有文件")

    for item in cleanup_items:
        if os.path.exists(item):
            try:
                shutil.rmtree(item)
                print(f"✓ 已删除: {item}")
            except Exception as e:
                print(f"✗ 删除失败 {item}: {e}")

    # 清理spec文件
    for spec_file in Path(".").glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"✓ 已删除: {spec_file}")
        except Exception as e:
            print(f"✗ 删除失败 {spec_file}: {e}")

    print("缓存清理完成")

def create_exe():
    """创建exe文件"""
    print("开始打包...")
    
    # 检查必要文件
    if not os.path.exists(PYTHON_FILE):
        print(f"✗ 找不到Python文件: {PYTHON_FILE}")
        return False
        
    if not os.path.exists(ICON_FILE):
        print(f"✗ 找不到图标文件: {ICON_FILE}")
        return False
    
    # 构建PyInstaller命令
    cmd = ["pyinstaller", "--onefile"]
    
    # 根据配置添加控制台参数
    if KEEP_CONSOLE:
        cmd.append("--console")
    else:
        cmd.append("--windowed")
    
    # 添加其他参数
    cmd.extend([
        f"--icon={ICON_FILE}",
        f"--name={EXE_NAME}",
        "--noconfirm",  # 移除--clean参数，避免删除已有文件
        PYTHON_FILE
    ])
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 打包成功!")
            
            # 检查生成的exe文件
            exe_path = os.path.join("dist", f"{EXE_NAME}.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"✓ 生成的exe文件: {exe_path}")
                print(f"✓ 文件大小: {file_size:.2f} MB")
                return True
            else:
                print("✗ 未找到生成的exe文件")
                return False
        else:
            print("✗ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 打包过程出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("    AI命名工具 - 打包脚本")
    print("=" * 50)
    print(f"Python文件: {PYTHON_FILE}")
    print(f"图标文件: {ICON_FILE}")
    print(f"exe名称: {EXE_NAME}")
    print(f"控制台窗口: {'保留' if KEEP_CONSOLE else '隐藏'}")
    print(f"清理dist文件夹: {'是' if CLEAN_DIST else '否（保留已有exe）'}")
    print("=" * 50)
    
    # 预清理
    clean_cache()
    
    # 创建exe
    success = create_exe()
    
    if success:
        print("\n" + "=" * 50)
        print("    打包完成!")
        print("=" * 50)
        print(f"✓ exe文件位置: dist/{EXE_NAME}.exe")
        print("✓ 运行exe时需要确保以下文件在同一目录:")
        print("  - api_keys.txt (API密钥文件)")
        print("  - vdf.json (配置文件)")
        print("  - key.vdf (授权文件)")
    else:
        print("\n" + "=" * 50)
        print("    打包失败!")
        print("=" * 50)
    
    # 最终清理
    print("\n最终清理...")
    clean_cache()
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
