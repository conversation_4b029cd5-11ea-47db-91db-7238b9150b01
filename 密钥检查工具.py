# -*- coding: utf-8 -*-
import os
import threading
import time
import json
import concurrent.futures
import requests
from datetime import datetime, timedelta
from openai import OpenAI
from colorama import init, Fore, Style
import sys
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from decimal import Decimal, getcontext

# 初始化colorama以支持彩色输出
init()

def validate_key():
    """验证 key.vdf 文件的有效性"""
    key_file = "key.vdf"
    if not os.path.exists(key_file):
        print(f"{Fore.RED}✗ 错误：未找到授权文件 {key_file}{Style.RESET_ALL}")
        sys.exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = os.getenv('KEY_PASSWORD', b'my_super_secret_password')
    salt = os.getenv('KEY_SALT', b'fixed_salt_value')

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    with open(key_file, "rb") as f:
        data = f.read()
        iv = data[:16]
        ciphertext = data[16:]

    # 解密数据
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    decryptor = cipher.decryptor()
    try:
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()
    except ValueError:
        print(f"{Fore.RED}✗ 错误：解密失败：数据可能被篡改{Style.RESET_ALL}")
        sys.exit(1)

    # 移除填充
    unpadder = padding.PKCS7(128).unpadder()
    try:
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()
    except ValueError:
        print(f"{Fore.RED}✗ 错误：数据校验失败：填充错误{Style.RESET_ALL}")
        sys.exit(1)

    # 验证时间有效性
    try:
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))
    except ValueError:
        print(f"{Fore.RED}✗ 错误：时间格式无效{Style.RESET_ALL}")
        sys.exit(1)

    # 有效期验证（30天）
    if datetime.now() - stored_time > timedelta(days=30):
        print(f"{Fore.RED}✗ 错误：软件授权已过期{Style.RESET_ALL}")
        sys.exit(1)
    
    print(f"{Fore.GREEN}✓ 授权验证通过{Style.RESET_ALL}")

def load_and_validate_keys(config):
    """加载API密钥并验证余额"""
    api_clients = []
    active_keys = []
    keys_status = {}
    
    try:
        # 检查密钥文件是否存在，不存在则提示用户并退出
        if not os.path.exists('api_keys.txt'):
            print(f"{Fore.RED}✗ 未找到密钥文件 api_keys.txt，请创建此文件并添加有效的API密钥{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}提示: 每行一个API密钥，保存为api_keys.txt文件{Style.RESET_ALL}")
            sys.exit(1)
        
        # 读取密钥
        with open('api_keys.txt', 'r', encoding='utf-8') as f:
            api_keys = [line.strip() for line in f.readlines() if line.strip()]
        
        if not api_keys:
            print(f"{Fore.RED}✗ 密钥文件为空，请添加有效的API密钥{Style.RESET_ALL}")
            sys.exit(1)
            
        print(f"{Fore.BLUE}i 共加载 {len(api_keys)} 个API密钥，正在并行验证密钥有效性...{Style.RESET_ALL}")
        
        # 验证每个密钥（使用多线程）
        base_url = config.get('api_base_url', 'https://api.siliconflow.cn/v1')
        
        # 线程安全的容器
        valid_keys_count = 0
        thread_lock = threading.Lock()
        start_time = time.time()
        
        # 进度显示变量
        progress_chars = ['◐', '◓', '◑', '◒']
        progress_idx = 0
        verified_count = 0
        total_keys = len(api_keys)
        
        def validate_key_func(key_index, key):
            nonlocal valid_keys_count, verified_count, progress_idx
            
            try:
                # 创建客户端实例
                client = OpenAI(api_key=key, base_url=base_url)
                
                # 验证密钥有效性并获取余额
                url = "https://api.siliconflow.cn/v1/user/info"
                headers = {"Authorization": f"Bearer {key}"}
                response = requests.get(url, headers=headers)
                
                with thread_lock:
                    verified_count += 1
                    # 更新进度显示
                    spinner = progress_chars[progress_idx]
                    progress_idx = (progress_idx + 1) % len(progress_chars)
                    percent = int(verified_count * 100 / total_keys)
                    sys.stdout.write(f"\r{Fore.BLUE}{spinner} 验证进度: [{verified_count}/{total_keys}] {percent}%{' '*20}{Style.RESET_ALL}")
                    sys.stdout.flush()
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") and "data" in data:
                        user_data = data["data"]
                        balance = float(user_data.get("balance", 0)) * 3  # 乘以3倍
                        
                        if float(balance) > 0:
                            with thread_lock:
                                api_clients.append(client)
                                active_keys.append(key)
                                keys_status[key] = {
                                    "balance": balance / 3,  # 存储原始余额，显示时再乘3
                                    "name": user_data.get("name", "未知用户")
                                }
                                valid_keys_count += 1
                                print(f"\r{Fore.GREEN}✓ 密钥 {key_index+1}/{total_keys} 验证成功: 余额 ¥{balance:.4f} ({user_data.get('name', '未知用户')}){' '*20}{Style.RESET_ALL}")
                        else:
                            print(f"\r{Fore.RED}✗ 密钥 {key_index+1}/{total_keys} 余额不足 (¥{balance:.4f}){' '*20}{Style.RESET_ALL}")
                    else:
                        print(f"\r{Fore.RED}✗ 密钥 {key_index+1}/{total_keys} 无效: {data.get('message', '未知错误')}{' '*20}{Style.RESET_ALL}")
                else:
                    print(f"\r{Fore.RED}✗ 密钥 {key_index+1}/{total_keys} 验证失败: HTTP {response.status_code}{' '*20}{Style.RESET_ALL}")
            except Exception as e:
                with thread_lock:
                    print(f"\r{Fore.RED}✗ 密钥 {key_index+1}/{total_keys} 验证出错: {str(e)}{' '*20}{Style.RESET_ALL}")
        
        # 使用线程池并行验证
        max_workers = min(10, len(api_keys))  # 最多10个线程
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(validate_key_func, i, key): i for i, key in enumerate(api_keys)}
            # 等待所有任务完成
            concurrent.futures.wait(futures)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n{Fore.BLUE}i 密钥验证完成，耗时: {duration:.2f}秒{Style.RESET_ALL}")
        
        if valid_keys_count > 0:
            print(f"{Fore.GREEN}✓ 共有 {valid_keys_count}/{len(api_keys)} 个有效密钥可用{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}✗ 所有密钥均无效或余额不足，请检查密钥文件{Style.RESET_ALL}")
            sys.exit(1)
            
        return api_clients, active_keys, keys_status
            
    except Exception as e:
        print(f"{Fore.RED}✗ 密钥加载或验证过程出错: {str(e)}{Style.RESET_ALL}")
        sys.exit(1)

def check_remaining_balance(config, active_keys, keys_status):
    """处理完毕后查询所有API密钥的剩余余额"""
    print(f"\n{Fore.CYAN}▌ 检查剩余余额 ▐{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}> 正在并行查询所有API密钥的剩余余额...{Style.RESET_ALL}")
    
    # 记录初始余额和最终余额（使用Decimal确保精确计算）
    getcontext().prec = 10  # 设置精度
    
    total_initial_balance = Decimal('0.0')
    total_final_balance = Decimal('0.0')
    success_count = 0
    failed_count = 0
    
    # 计算初始总余额
    for key in active_keys:
        if key in keys_status:
            balance = Decimal(str(keys_status[key].get("balance", 0))) * Decimal('3')
            total_initial_balance += balance
    
    # 线程安全的容器
    final_balances = {}
    thread_lock = threading.Lock()
    start_time = time.time()
    
    # 查询单个密钥的余额
    def query_balance(key):
        nonlocal success_count, failed_count
        
        try:
            base_url = config.get('api_base_url', 'https://api.siliconflow.cn/v1')
            url = f"{base_url}/user/info"
            headers = {"Authorization": f"Bearer {key}"}
            key_short = f"{key[:8]}...{key[-8:]}"  # 用于显示的简短密钥形式
            
            response = requests.get(url, headers=headers)
            
            with thread_lock:
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") and "data" in data:
                        user_data = data["data"]
                        # 使用Decimal确保精确计算
                        current_balance = Decimal(str(user_data.get("balance", 0))) * Decimal('3')
                        final_balances[key] = current_balance
                        
                        # 显示每个密钥的余额
                        user_name = keys_status[key].get("name", "未知用户")
                        print(f"{Fore.BLUE}i 密钥 {key_short} ({user_name}) 当前余额: ¥{float(current_balance):.4f}{Style.RESET_ALL}")
                        
                        success_count += 1
                    else:
                        print(f"{Fore.RED}✗ 查询密钥 {key_short} 余额返回无效数据{Style.RESET_ALL}")
                        failed_count += 1
                else:
                    print(f"{Fore.RED}✗ 查询密钥 {key_short} 余额失败: HTTP {response.status_code}{Style.RESET_ALL}")
                    failed_count += 1
        
        except Exception as e:
            with thread_lock:
                key_short = f"{key[:8]}...{key[-8:]}"
                print(f"{Fore.RED}✗ 查询密钥 {key_short} 余额异常: {str(e)}{Style.RESET_ALL}")
                failed_count += 1
    
    # 使用线程池并行查询
    max_workers = min(10, len(active_keys))  # 最多10个线程
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(query_balance, key): key for key in active_keys}
        # 等待所有任务完成
        concurrent.futures.wait(futures)
    
    # 计算最终总余额
    for key, balance in final_balances.items():
        total_final_balance += balance
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 显示总消费和查询统计
    print(f"\n{Fore.GREEN}▌ 余额查询统计 ▐{Style.RESET_ALL}")
    print(f"{Fore.CYAN}└─ 成功查询: {success_count}/{len(active_keys)}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}└─ 查询失败: {failed_count}/{len(active_keys)}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}└─ 查询耗时: {duration:.2f}秒{Style.RESET_ALL}")
    
    if success_count > 0:
        # 计算总消费（初始总余额 - 最终总余额）
        total_spent = total_initial_balance - total_final_balance
        
        # 显示总消费
        print(f"\n{Fore.GREEN}▌ 总消费统计 ▐{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 初始总余额: ¥{float(total_initial_balance):.4f}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 当前总余额: ¥{float(total_final_balance):.4f}{Style.RESET_ALL}")
        
        # 只有当消费大于0.001元时才显示消费金额，避免浮点数精度问题
        if total_spent > Decimal('0.001'):
            print(f"{Fore.GREEN}└─ 本次操作总消费: ¥{float(total_spent):.4f}{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}└─ 本次操作总消费: 微量或无消费 (API调用费用过小){Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}✗ 所有密钥余额查询均失败，无法计算实际消费{Style.RESET_ALL}")

if __name__ == "__main__":
    # 测试功能
    print("密钥检查工具测试")
    validate_key()
    
    # 加载配置文件
    try:
        with open('vdf.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"配置文件加载失败：{str(e)}")
        config = {}
    
    # 测试密钥验证
    api_clients, active_keys, keys_status = load_and_validate_keys(config)
    print(f"验证完成，有效密钥数量：{len(active_keys)}")
    
    # 测试余额查询
    check_remaining_balance(config, active_keys, keys_status)
